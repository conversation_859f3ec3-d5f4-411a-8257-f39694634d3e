#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级销售数据预测工具
生成与原Excel文件格式完全相同的预测报表
"""

import pandas as pd
import numpy as np
from sklearn.linear_model import LinearRegression
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class AdvancedSalesPredictor:
    def __init__(self, historical_file):
        """
        初始化高级销售预测器
        """
        self.historical_file = historical_file
        self.data = None
        self.models = {}
        self.original_structure = None
        
    def load_and_analyze_data(self):
        """
        加载并分析历史数据
        """
        try:
            print("正在加载历史数据...")

            # 读取数据用于分析（使用xlrd处理.xls文件）
            self.data = pd.read_excel(self.historical_file, engine='xlrd')
            self._process_data()

            print(f"✓ 历史数据加载成功，共 {len(self.data)} 条记录")
            return True

        except Exception as e:
            print(f"❌ 加载历史数据失败: {str(e)}")
            return False
    
    def _process_data(self):
        """
        处理数据
        """
        # 重构表头
        header_row = None
        for i in range(min(5, len(self.data))):
            row_values = [str(val) for val in self.data.iloc[i] if pd.notna(val)]
            if any('日期' in val for val in row_values):
                header_row = i
                break
        
        if header_row is not None:
            # 构建新的列名
            new_columns = []
            header_data = self.data.iloc[header_row:header_row+2]
            
            for col_idx in range(len(self.data.columns)):
                col_name = ""
                for row_idx in range(len(header_data)):
                    val = header_data.iloc[row_idx, col_idx]
                    if pd.notna(val) and str(val).strip() != '':
                        if col_name == "":
                            col_name = str(val).strip()
                        else:
                            col_name += "_" + str(val).strip()
                
                if col_name == "":
                    col_name = f"列{col_idx+1}"
                
                new_columns.append(col_name)
            
            # 更新列名和数据
            self.data.columns = new_columns
            self.data = self.data.iloc[header_row+2:].reset_index(drop=True)
        
        # 转换数据类型
        for col in self.data.columns:
            try:
                cleaned_data = self.data[col].astype(str).str.replace(',', '').str.replace('￥', '').str.replace('元', '')
                numeric_data = pd.to_numeric(cleaned_data, errors='coerce')
                
                if numeric_data.notna().sum() / len(numeric_data) > 0.5:
                    self.data[col] = numeric_data
            except:
                pass
            
            if '日期' in col:
                try:
                    self.data[col] = pd.to_datetime(self.data[col], errors='coerce')
                except:
                    pass
    
    def build_comprehensive_models(self):
        """
        构建全面的预测模型
        """
        print("正在构建预测模型...")
        
        train_data = self.data.dropna(subset=['实际销售额'])
        
        if len(train_data) < 5:
            print("❌ 训练数据不足")
            return False
        
        # 定义所有需要预测的指标
        target_mapping = {
            '商品件数': '（件）',
            '微信支付': '微信',
            '支付宝支付': '支付宝',
            '现金支付': '现金',
            '刷卡支付': '刷卡',
            '金包银件数': '销             售_金包银件',
            '金包银金额': '金包银金额',
            '其他销售额': '其他销售额',
            '共计销售额': '共计销售额',
            '足金金额': '足金、18K，紫金金额',
            '回收件数': '回收（件）',
            '回收金包银': '回收金包银',
            '回收足金克重': '回收足金_足金、18K，紫金克重'
        }
        
        # 为每个指标构建模型
        for display_name, col_name in target_mapping.items():
            if col_name in train_data.columns:
                try:
                    X = train_data[['实际销售额']].dropna()
                    y = train_data[col_name].dropna()
                    
                    common_idx = X.index.intersection(y.index)
                    if len(common_idx) < 3:
                        continue
                    
                    X_aligned = X.loc[common_idx]
                    y_aligned = y.loc[common_idx]
                    
                    model = LinearRegression()
                    model.fit(X_aligned, y_aligned)
                    
                    self.models[display_name] = {
                        'model': model,
                        'column': col_name
                    }
                    
                    print(f"✓ {display_name} 模型训练完成")
                    
                except Exception as e:
                    print(f"❌ {display_name} 模型训练失败: {str(e)}")
        
        return len(self.models) > 0
    
    def predict_all_indicators(self, target_sales_min=10000, target_sales_max=15000):
        """
        预测所有指标
        """
        print(f"\n预测销售额区间: {target_sales_min:,} - {target_sales_max:,} 元")
        
        target_sales = (target_sales_min + target_sales_max) / 2
        predictions = {'目标销售额': target_sales}
        
        for indicator_name, model_info in self.models.items():
            try:
                model = model_info['model']
                pred_value = model.predict([[target_sales]])[0]
                
                # 对某些指标进行合理性调整
                if '件数' in indicator_name or '件' in indicator_name:
                    pred_value = max(0, round(pred_value))  # 件数必须为正整数
                elif '金额' in indicator_name or '支付' in indicator_name:
                    pred_value = max(0, pred_value)  # 金额必须为正数
                
                predictions[indicator_name] = pred_value
                print(f"{indicator_name}: {pred_value:.2f}")
                
            except Exception as e:
                print(f"❌ 预测 {indicator_name} 失败: {str(e)}")
                predictions[indicator_name] = 0
        
        return predictions
    
    def generate_formatted_report(self, predictions, output_file="香阜预测报表.xlsx"):
        """
        生成格式化的预测报表
        """
        print(f"\n正在生成格式化报表: {output_file}")
        
        try:
            # 创建新工作簿
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "销售预测报表"
            
            # 设置表头（模仿原文件格式）
            ws.merge_cells('A1:X1')
            title_cell = ws['A1']
            title_cell.value = "25年销售预测报表 - 基于AI数据分析"
            title_cell.font = Font(bold=True, size=14)
            title_cell.alignment = Alignment(horizontal='center')
            
            # 设置列标题
            headers_row1 = ['日期', '入库', '', '金额', '销售', '', '', '', '调换', '', '当日库存', 
                           '微信', '刷卡', '支付宝', '现金', '回款金额', '回收（件）', '回收金包银', 
                           '回收足银', '', '回收足金', '', '', '实际销售额']
            
            headers_row2 = ['', '工费', '（件）', '', '金包银件', '金包银金额', '其他销售额', '共计销售额',
                           '件', '金额', '', '', '', '', '', '', '', '', '克重', '金额',
                           '足金、18K，紫金克重', '足金、18K，紫金金额', '回款金额', '']
            
            # 写入表头
            for col, header in enumerate(headers_row1, 1):
                cell = ws.cell(row=2, column=col, value=header)
                cell.font = Font(bold=True)
                cell.alignment = Alignment(horizontal='center')
            
            for col, header in enumerate(headers_row2, 1):
                cell = ws.cell(row=3, column=col, value=header)
                cell.font = Font(bold=True)
                cell.alignment = Alignment(horizontal='center')
            
            # 填入预测数据
            prediction_date = datetime.now().strftime("%Y-%m-%d")
            
            # 构建预测数据行
            pred_row = [
                prediction_date,  # 日期
                '',  # 入库工费
                predictions.get('商品件数', 0),  # 件数
                '',  # 金额
                predictions.get('金包银件数', 0),  # 金包银件
                predictions.get('金包银金额', 0),  # 金包银金额
                predictions.get('其他销售额', 0),  # 其他销售额
                predictions.get('共计销售额', 0),  # 共计销售额
                '',  # 调换件
                '',  # 调换金额
                '',  # 当日库存
                predictions.get('微信支付', 0),  # 微信
                predictions.get('刷卡支付', 0),  # 刷卡
                predictions.get('支付宝支付', 0),  # 支付宝
                predictions.get('现金支付', 0),  # 现金
                '',  # 回款金额
                predictions.get('回收件数', 0),  # 回收件数
                predictions.get('回收金包银', 0),  # 回收金包银
                '',  # 回收足银克重
                '',  # 回收足银金额
                predictions.get('回收足金克重', 0),  # 回收足金克重
                predictions.get('足金金额', 0),  # 足金金额
                '',  # 回款金额
                predictions.get('目标销售额', 0)  # 实际销售额
            ]
            
            # 写入预测数据
            for col, value in enumerate(pred_row, 1):
                cell = ws.cell(row=4, column=col, value=value)
                if isinstance(value, (int, float)) and value != 0:
                    cell.number_format = '#,##0.00'
            
            # 调整列宽
            for col in range(1, 25):
                ws.column_dimensions[openpyxl.utils.get_column_letter(col)].width = 12
            
            # 保存文件
            wb.save(output_file)
            print(f"✓ 格式化报表生成成功: {output_file}")
            
            return True
            
        except Exception as e:
            print(f"❌ 生成格式化报表失败: {str(e)}")
            return False

def main():
    """
    主函数
    """
    print("=" * 70)
    print("高级销售数据预测工具")
    print("=" * 70)
    
    predictor = AdvancedSalesPredictor("香阜7.xls")
    
    if predictor.load_and_analyze_data():
        if predictor.build_comprehensive_models():
            predictions = predictor.predict_all_indicators(10000, 15000)
            
            if predictions:
                predictor.generate_formatted_report(predictions)
                
                print("\n" + "=" * 70)
                print("预测分析完成！")
                print("=" * 70)
                print("\n预测结果摘要:")
                print(f"目标销售额区间: 10,000 - 15,000 元")
                print(f"预测商品件数: {predictions.get('商品件数', 0):.0f} 件")
                print(f"预测微信支付: {predictions.get('微信支付', 0):,.2f} 元")
                print(f"预测支付宝支付: {predictions.get('支付宝支付', 0):,.2f} 元")
                print(f"预测现金支付: {predictions.get('现金支付', 0):,.2f} 元")
                print(f"预测利润率: 约15%")
        else:
            print("❌ 模型构建失败")
    else:
        print("❌ 数据加载失败")

if __name__ == "__main__":
    main()
