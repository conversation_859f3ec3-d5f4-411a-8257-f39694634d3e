#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
销售数据预测工具
基于历史数据预测销售指标并生成Excel报表
"""

import pandas as pd
import numpy as np
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class SalesPredictor:
    def __init__(self, historical_file):
        """
        初始化销售预测器
        
        Args:
            historical_file (str): 历史数据Excel文件路径
        """
        self.historical_file = historical_file
        self.data = None
        self.models = {}
        self.column_mapping = {}
        
    def load_historical_data(self):
        """
        加载历史数据
        """
        try:
            print("正在加载历史数据...")
            
            # 读取Excel文件
            self.data = pd.read_excel(self.historical_file, engine='xlrd')
            
            # 重构表头
            self._reconstruct_headers()
            
            # 转换数据类型
            self._convert_data_types()
            
            print(f"✓ 历史数据加载成功，共 {len(self.data)} 条记录")
            return True
            
        except Exception as e:
            print(f"❌ 加载历史数据失败: {str(e)}")
            return False
    
    def _reconstruct_headers(self):
        """
        重构表头
        """
        # 查找表头行
        header_row = None
        for i in range(min(5, len(self.data))):
            row_values = [str(val) for val in self.data.iloc[i] if pd.notna(val)]
            if any('日期' in val for val in row_values):
                header_row = i
                break
        
        if header_row is not None:
            # 构建新的列名
            new_columns = []
            header_data = self.data.iloc[header_row:header_row+2]
            
            for col_idx in range(len(self.data.columns)):
                col_name = ""
                for row_idx in range(len(header_data)):
                    val = header_data.iloc[row_idx, col_idx]
                    if pd.notna(val) and str(val).strip() != '':
                        if col_name == "":
                            col_name = str(val).strip()
                        else:
                            col_name += "_" + str(val).strip()
                
                if col_name == "":
                    col_name = f"列{col_idx+1}"
                
                new_columns.append(col_name)
            
            # 更新列名和数据
            self.data.columns = new_columns
            self.data = self.data.iloc[header_row+2:].reset_index(drop=True)
    
    def _convert_data_types(self):
        """
        转换数据类型
        """
        for col in self.data.columns:
            # 转换数值类型
            try:
                cleaned_data = self.data[col].astype(str).str.replace(',', '').str.replace('￥', '').str.replace('元', '')
                numeric_data = pd.to_numeric(cleaned_data, errors='coerce')
                
                if numeric_data.notna().sum() / len(numeric_data) > 0.5:
                    self.data[col] = numeric_data
            except:
                pass
            
            # 转换日期类型
            if '日期' in col:
                try:
                    self.data[col] = pd.to_datetime(self.data[col], errors='coerce')
                except:
                    pass
    
    def build_models(self):
        """
        构建预测模型
        """
        print("正在构建预测模型...")
        
        # 准备训练数据
        train_data = self.data.dropna(subset=['实际销售额'])
        
        if len(train_data) < 5:
            print("❌ 训练数据不足")
            return False
        
        # 定义目标列
        target_columns = {
            '商品件数': '（件）',
            '微信支付': '微信',
            '支付宝支付': '支付宝',
            '现金支付': '现金',
            '刷卡支付': '刷卡',
            '金包银金额': '金包银金额',
            '足金金额': '足金、18K，紫金金额',
            '回收金额': '回收金包银'
        }
        
        # 为每个目标指标构建模型
        for display_name, col_name in target_columns.items():
            if col_name in train_data.columns:
                try:
                    # 准备特征和目标
                    X = train_data[['实际销售额']].dropna()
                    y = train_data[col_name].dropna()
                    
                    # 对齐索引
                    common_idx = X.index.intersection(y.index)
                    if len(common_idx) < 3:
                        continue
                    
                    X_aligned = X.loc[common_idx]
                    y_aligned = y.loc[common_idx]
                    
                    # 训练模型
                    model = LinearRegression()
                    model.fit(X_aligned, y_aligned)
                    
                    self.models[display_name] = {
                        'model': model,
                        'column': col_name
                    }
                    
                    print(f"✓ {display_name} 模型训练完成")
                    
                except Exception as e:
                    print(f"❌ {display_name} 模型训练失败: {str(e)}")
        
        return len(self.models) > 0
    
    def predict_indicators(self, target_sales_min=10000, target_sales_max=15000):
        """
        预测各项指标
        """
        print(f"\n预测销售额区间: {target_sales_min:,} - {target_sales_max:,} 元")
        
        # 使用区间中位数
        target_sales = (target_sales_min + target_sales_max) / 2
        
        predictions = {}
        
        for indicator_name, model_info in self.models.items():
            try:
                model = model_info['model']
                pred_value = model.predict([[target_sales]])[0]
                predictions[indicator_name] = max(0, pred_value)  # 确保预测值非负
                print(f"{indicator_name}: {pred_value:.2f}")
            except Exception as e:
                print(f"❌ 预测 {indicator_name} 失败: {str(e)}")
                predictions[indicator_name] = 0
        
        return predictions
    
    def generate_excel_report(self, predictions, output_file="预测销售报表.xlsx"):
        """
        生成Excel报表
        """
        print(f"\n正在生成Excel报表: {output_file}")
        
        try:
            # 创建工作簿
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "销售预测报表"
            
            # 设置表头
            headers = [
                "预测日期", "预测销售额区间", "商品件数", "微信支付", "支付宝支付", 
                "现金支付", "刷卡支付", "金包银金额", "足金金额", "回收金额", "预计利润"
            ]
            
            # 写入表头
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True)
                cell.alignment = Alignment(horizontal='center')

            # 计算预测数据
            target_sales_min, target_sales_max = 10000, 15000
            sales_range = f"{target_sales_min:,} - {target_sales_max:,} 元"

            # 预计利润（简单估算为销售额的15%）
            estimated_profit = (target_sales_min + target_sales_max) / 2 * 0.15

            # 写入预测数据
            row_data = [
                datetime.now().strftime("%Y-%m-%d"),
                sales_range,
                predictions.get('商品件数', 0),
                predictions.get('微信支付', 0),
                predictions.get('支付宝支付', 0),
                predictions.get('现金支付', 0),
                predictions.get('刷卡支付', 0),
                predictions.get('金包银金额', 0),
                predictions.get('足金金额', 0),
                predictions.get('回收金额', 0),
                estimated_profit
            ]

            for col, value in enumerate(row_data, 1):
                cell = ws.cell(row=2, column=col, value=value)
                if isinstance(value, (int, float)) and col > 2:
                    cell.number_format = '#,##0.00'
            
            # 调整列宽
            for col in range(1, len(headers) + 1):
                ws.column_dimensions[openpyxl.utils.get_column_letter(col)].width = 15
            
            # 保存文件
            wb.save(output_file)
            print(f"✓ Excel报表生成成功: {output_file}")
            
            return True
            
        except Exception as e:
            print(f"❌ 生成Excel报表失败: {str(e)}")
            return False

def main():
    """
    主函数
    """
    print("=" * 60)
    print("销售数据预测工具")
    print("=" * 60)
    
    # 创建预测器
    predictor = SalesPredictor("香阜7.xls")
    
    # 加载历史数据
    if predictor.load_historical_data():
        # 构建模型
        if predictor.build_models():
            # 进行预测
            predictions = predictor.predict_indicators(10000, 15000)
            
            # 生成Excel报表
            predictor.generate_excel_report(predictions)
            
            print("\n" + "=" * 60)
            print("预测完成！")
            print("=" * 60)
        else:
            print("❌ 模型构建失败")
    else:
        print("❌ 数据加载失败")

if __name__ == "__main__":
    main()
