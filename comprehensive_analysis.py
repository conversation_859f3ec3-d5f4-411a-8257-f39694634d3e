#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合销售数据分析报告工具
提供完整的数据分析、预测和报告生成功能
"""

import pandas as pd
import numpy as np
from sklearn.linear_model import LinearRegression
from sklearn.metrics import r2_score, mean_absolute_error
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class ComprehensiveAnalyzer:
    def __init__(self, historical_file):
        """
        初始化综合分析器
        """
        self.historical_file = historical_file
        self.data = None
        self.models = {}
        self.analysis_results = {}
        
    def run_complete_analysis(self):
        """
        运行完整分析流程
        """
        print("=" * 80)
        print("综合销售数据分析报告")
        print("=" * 80)
        
        # 步骤1: 数据加载和预处理
        if not self._load_and_process_data():
            return False
        
        # 步骤2: 数据质量分析
        self._analyze_data_quality()
        
        # 步骤3: 销售模式分析
        self._analyze_sales_patterns()
        
        # 步骤4: 构建预测模型
        self._build_prediction_models()
        
        # 步骤5: 模型评估
        self._evaluate_models()
        
        # 步骤6: 生成预测
        predictions = self._generate_predictions()
        
        # 步骤7: 生成综合报告
        self._generate_comprehensive_report(predictions)
        
        return True
    
    def _load_and_process_data(self):
        """
        加载和处理数据
        """
        try:
            print("\n1. 数据加载和预处理")
            print("-" * 40)
            
            self.data = pd.read_excel(self.historical_file, engine='xlrd')
            
            # 重构表头
            header_row = None
            for i in range(min(5, len(self.data))):
                row_values = [str(val) for val in self.data.iloc[i] if pd.notna(val)]
                if any('日期' in val for val in row_values):
                    header_row = i
                    break
            
            if header_row is not None:
                new_columns = []
                header_data = self.data.iloc[header_row:header_row+2]
                
                for col_idx in range(len(self.data.columns)):
                    col_name = ""
                    for row_idx in range(len(header_data)):
                        val = header_data.iloc[row_idx, col_idx]
                        if pd.notna(val) and str(val).strip() != '':
                            if col_name == "":
                                col_name = str(val).strip()
                            else:
                                col_name += "_" + str(val).strip()
                    
                    if col_name == "":
                        col_name = f"列{col_idx+1}"
                    
                    new_columns.append(col_name)
                
                self.data.columns = new_columns
                self.data = self.data.iloc[header_row+2:].reset_index(drop=True)
            
            # 数据类型转换
            for col in self.data.columns:
                try:
                    cleaned_data = self.data[col].astype(str).str.replace(',', '').str.replace('￥', '').str.replace('元', '')
                    numeric_data = pd.to_numeric(cleaned_data, errors='coerce')
                    
                    if numeric_data.notna().sum() / len(numeric_data) > 0.5:
                        self.data[col] = numeric_data
                except:
                    pass
                
                if '日期' in col:
                    try:
                        self.data[col] = pd.to_datetime(self.data[col], errors='coerce')
                    except:
                        pass
            
            print(f"✓ 数据加载成功: {len(self.data)} 条记录, {len(self.data.columns)} 个字段")
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {str(e)}")
            return False
    
    def _analyze_data_quality(self):
        """
        分析数据质量
        """
        print("\n2. 数据质量分析")
        print("-" * 40)
        
        # 缺失值分析
        missing_data = self.data.isnull().sum()
        missing_percent = (missing_data / len(self.data)) * 100
        
        print("主要字段缺失值情况:")
        key_fields = ['实际销售额', '微信', '支付宝', '现金', '（件）']
        for field in key_fields:
            if field in self.data.columns:
                missing_count = missing_data[field]
                missing_pct = missing_percent[field]
                print(f"  {field}: {missing_count} 条 ({missing_pct:.1f}%)")
        
        # 数据范围分析
        numeric_cols = self.data.select_dtypes(include=[np.number]).columns
        print(f"\n数值字段统计 (共{len(numeric_cols)}个):")
        
        if '实际销售额' in numeric_cols:
            sales_data = self.data['实际销售额'].dropna()
            print(f"  实际销售额: 最小值={sales_data.min():.2f}, 最大值={sales_data.max():.2f}, 平均值={sales_data.mean():.2f}")
        
        self.analysis_results['data_quality'] = {
            'total_records': len(self.data),
            'missing_data': missing_data.to_dict(),
            'numeric_fields': len(numeric_cols)
        }
    
    def _analyze_sales_patterns(self):
        """
        分析销售模式
        """
        print("\n3. 销售模式分析")
        print("-" * 40)
        
        if '实际销售额' not in self.data.columns:
            print("❌ 未找到销售额数据")
            return
        
        sales_data = self.data['实际销售额'].dropna()
        
        # 销售额分布分析
        print("销售额分布分析:")
        print(f"  平均销售额: {sales_data.mean():,.2f} 元")
        print(f"  中位数销售额: {sales_data.median():,.2f} 元")
        print(f"  标准差: {sales_data.std():,.2f} 元")
        
        # 目标区间分析
        target_range = (sales_data >= 10000) & (sales_data <= 15000)
        target_count = target_range.sum()
        target_percent = (target_count / len(sales_data)) * 100
        
        print(f"\n目标区间(10,000-15,000元)分析:")
        print(f"  符合条件天数: {target_count} 天")
        print(f"  占比: {target_percent:.1f}%")
        
        if target_count > 0:
            target_data = sales_data[target_range]
            print(f"  区间内平均值: {target_data.mean():,.2f} 元")
        
        self.analysis_results['sales_patterns'] = {
            'mean_sales': sales_data.mean(),
            'median_sales': sales_data.median(),
            'std_sales': sales_data.std(),
            'target_range_count': target_count,
            'target_range_percent': target_percent
        }
    
    def _build_prediction_models(self):
        """
        构建预测模型
        """
        print("\n4. 预测模型构建")
        print("-" * 40)
        
        train_data = self.data.dropna(subset=['实际销售额'])
        
        target_mapping = {
            '商品件数': '（件）',
            '微信支付': '微信',
            '支付宝支付': '支付宝',
            '现金支付': '现金',
            '金包银金额': '金包银金额',
            '足金金额': '足金、18K，紫金金额',
            '回收金额': '回收金包银'
        }
        
        model_count = 0
        for display_name, col_name in target_mapping.items():
            if col_name in train_data.columns:
                try:
                    X = train_data[['实际销售额']].dropna()
                    y = train_data[col_name].dropna()
                    
                    common_idx = X.index.intersection(y.index)
                    if len(common_idx) < 3:
                        continue
                    
                    X_aligned = X.loc[common_idx]
                    y_aligned = y.loc[common_idx]
                    
                    model = LinearRegression()
                    model.fit(X_aligned, y_aligned)
                    
                    # 计算模型评估指标
                    y_pred = model.predict(X_aligned)
                    r2 = r2_score(y_aligned, y_pred)
                    mae = mean_absolute_error(y_aligned, y_pred)
                    
                    self.models[display_name] = {
                        'model': model,
                        'column': col_name,
                        'r2_score': r2,
                        'mae': mae,
                        'sample_size': len(common_idx)
                    }
                    
                    model_count += 1
                    print(f"  ✓ {display_name}: R²={r2:.3f}, MAE={mae:.2f}")
                    
                except Exception as e:
                    print(f"  ❌ {display_name}: {str(e)}")
        
        print(f"\n成功构建 {model_count} 个预测模型")
        self.analysis_results['model_count'] = model_count
    
    def _evaluate_models(self):
        """
        评估模型性能
        """
        print("\n5. 模型性能评估")
        print("-" * 40)
        
        if not self.models:
            print("❌ 没有可用的模型")
            return
        
        print("模型准确性评估:")
        for name, info in self.models.items():
            r2 = info['r2_score']
            mae = info['mae']
            sample_size = info['sample_size']
            
            if r2 > 0.7:
                quality = "优秀"
            elif r2 > 0.5:
                quality = "良好"
            elif r2 > 0.3:
                quality = "一般"
            else:
                quality = "较差"
            
            print(f"  {name}: {quality} (R²={r2:.3f}, 样本数={sample_size})")
    
    def _generate_predictions(self, target_sales_min=10000, target_sales_max=15000):
        """
        生成预测结果
        """
        print(f"\n6. 预测结果生成")
        print("-" * 40)
        
        target_sales = (target_sales_min + target_sales_max) / 2
        predictions = {'目标销售额': target_sales}
        
        print(f"目标销售额: {target_sales:,.0f} 元")
        print("预测结果:")
        
        for indicator_name, model_info in self.models.items():
            try:
                model = model_info['model']
                pred_value = model.predict([[target_sales]])[0]
                
                if '件数' in indicator_name:
                    pred_value = max(0, round(pred_value))
                elif '金额' in indicator_name or '支付' in indicator_name:
                    pred_value = max(0, pred_value)
                
                predictions[indicator_name] = pred_value
                print(f"  {indicator_name}: {pred_value:,.2f}")
                
            except Exception as e:
                predictions[indicator_name] = 0
        
        return predictions
    
    def _generate_comprehensive_report(self, predictions):
        """
        生成综合报告
        """
        print(f"\n7. 综合报告生成")
        print("-" * 40)
        
        # 生成Excel报告
        wb = openpyxl.Workbook()
        
        # 创建预测结果工作表
        ws1 = wb.active
        ws1.title = "预测结果"
        
        # 写入预测结果
        ws1['A1'] = "销售预测分析报告"
        ws1['A1'].font = Font(bold=True, size=16)
        
        row = 3
        ws1[f'A{row}'] = "预测指标"
        ws1[f'B{row}'] = "预测值"
        ws1[f'A{row}'].font = Font(bold=True)
        ws1[f'B{row}'].font = Font(bold=True)
        
        for indicator, value in predictions.items():
            row += 1
            ws1[f'A{row}'] = indicator
            ws1[f'B{row}'] = value
            if isinstance(value, (int, float)):
                ws1[f'B{row}'].number_format = '#,##0.00'
        
        # 创建模型评估工作表
        ws2 = wb.create_sheet("模型评估")
        ws2['A1'] = "模型性能评估"
        ws2['A1'].font = Font(bold=True, size=14)
        
        headers = ['模型名称', 'R²得分', '平均绝对误差', '样本数量']
        for col, header in enumerate(headers, 1):
            ws2.cell(row=3, column=col, value=header).font = Font(bold=True)
        
        row = 4
        for name, info in self.models.items():
            ws2.cell(row=row, column=1, value=name)
            ws2.cell(row=row, column=2, value=info['r2_score'])
            ws2.cell(row=row, column=3, value=info['mae'])
            ws2.cell(row=row, column=4, value=info['sample_size'])
            row += 1
        
        # 保存报告
        report_file = f"综合分析报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        wb.save(report_file)
        
        print(f"✓ 综合报告已生成: {report_file}")
        
        # 打印总结
        print(f"\n" + "=" * 80)
        print("分析总结")
        print("=" * 80)
        print(f"✓ 数据质量: 共{self.analysis_results['data_quality']['total_records']}条记录")
        print(f"✓ 模型数量: {self.analysis_results['model_count']}个预测模型")
        print(f"✓ 目标区间覆盖率: {self.analysis_results['sales_patterns']['target_range_percent']:.1f}%")
        print(f"✓ 预测准确性: 基于线性回归模型，适用于趋势预测")
        print(f"✓ 建议: 定期更新历史数据以提高预测准确性")

def main():
    """
    主函数
    """
    analyzer = ComprehensiveAnalyzer("香阜7.xls")
    analyzer.run_complete_analysis()

if __name__ == "__main__":
    main()
