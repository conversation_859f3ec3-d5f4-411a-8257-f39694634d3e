#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel销售数据分析工具
分析香阜7.xls文件的结构和数据模式，并基于历史数据预测销售指标
"""

import pandas as pd
import numpy as np
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

class SalesDataAnalyzer:
    def __init__(self, excel_file_path):
        """
        初始化销售数据分析器
        
        Args:
            excel_file_path (str): Excel文件路径
        """
        self.excel_file_path = excel_file_path
        self.data = None
        self.models = {}
        self.scalers = {}
        
    def load_and_analyze_structure(self):
        """
        加载Excel文件并分析数据结构
        """
        try:
            print(f"正在分析Excel文件: {self.excel_file_path}")

            # 尝试读取Excel文件
            try:
                # 先尝试用openpyxl引擎读取
                self.data = pd.read_excel(self.excel_file_path, engine='openpyxl')
            except:
                try:
                    # 如果失败，尝试用xlrd引擎读取
                    self.data = pd.read_excel(self.excel_file_path, engine='xlrd')
                except:
                    # 最后尝试默认引擎
                    self.data = pd.read_excel(self.excel_file_path)

            print("✓ Excel文件加载成功")
            print(f"原始数据形状: {self.data.shape}")

            # 分析和重构表头
            self._reconstruct_headers()

            print(f"处理后数据形状: {self.data.shape}")
            print(f"列名: {list(self.data.columns)}")
            print("\n数据前10行:")
            print(self.data.head(10))

            # 转换数据类型
            self._convert_data_types()

            print("\n数据类型:")
            print(self.data.dtypes)
            print("\n数据统计信息:")
            print(self.data.describe())

            # 检查缺失值
            print("\n缺失值统计:")
            print(self.data.isnull().sum())

            return True

        except Exception as e:
            print(f"❌ 加载Excel文件失败: {str(e)}")
            return False

    def _reconstruct_headers(self):
        """
        重构表头，处理合并单元格和多行表头的情况
        """
        print("\n正在重构表头...")

        # 查看前几行数据，寻找真正的表头
        print("原始前5行数据:")
        for i in range(min(5, len(self.data))):
            print(f"第{i}行: {list(self.data.iloc[i])}")

        # 寻找包含"日期"的行作为表头起始行
        header_row = None
        for i in range(min(5, len(self.data))):
            row_values = [str(val) for val in self.data.iloc[i] if pd.notna(val)]
            if any('日期' in val for val in row_values):
                header_row = i
                break

        if header_row is not None:
            print(f"找到表头行: 第{header_row}行")

            # 构建新的列名
            new_columns = []
            header_data = self.data.iloc[header_row:header_row+2]  # 取两行作为表头

            for col_idx in range(len(self.data.columns)):
                col_name = ""
                for row_idx in range(len(header_data)):
                    val = header_data.iloc[row_idx, col_idx]
                    if pd.notna(val) and str(val).strip() != '':
                        if col_name == "":
                            col_name = str(val).strip()
                        else:
                            col_name += "_" + str(val).strip()

                if col_name == "":
                    col_name = f"列{col_idx+1}"

                new_columns.append(col_name)

            # 更新列名和数据
            self.data.columns = new_columns
            self.data = self.data.iloc[header_row+2:].reset_index(drop=True)

            print(f"新列名: {new_columns}")
        else:
            print("未找到明确的表头行，使用原始结构")

    def _convert_data_types(self):
        """
        转换数据类型
        """
        print("\n正在转换数据类型...")

        for col in self.data.columns:
            # 尝试转换为数值类型
            try:
                # 先清理数据
                cleaned_data = self.data[col].astype(str).str.replace(',', '').str.replace('￥', '').str.replace('元', '')
                numeric_data = pd.to_numeric(cleaned_data, errors='coerce')

                # 如果转换成功的比例超过50%，则认为是数值列
                if numeric_data.notna().sum() / len(numeric_data) > 0.5:
                    self.data[col] = numeric_data
                    print(f"列 '{col}' 转换为数值类型")
            except:
                pass

            # 尝试转换日期类型
            if '日期' in col or 'date' in col.lower():
                try:
                    self.data[col] = pd.to_datetime(self.data[col], errors='coerce')
                    print(f"列 '{col}' 转换为日期类型")
                except:
                    pass
    
    def identify_sales_columns(self):
        """
        识别销售相关的列
        """
        if self.data is None:
            print("❌ 请先加载数据")
            return None
            
        # 常见的销售数据列名关键词
        sales_keywords = ['销售', '金额', '收入', '营业额', '总额', '合计']
        payment_keywords = ['微信', '支付宝', '现金', '刷卡', '支付']
        quantity_keywords = ['数量', '件数', '个数', '销量']
        profit_keywords = ['利润', '毛利', '净利']
        
        identified_columns = {
            'sales_amount': [],
            'payment_methods': [],
            'quantities': [],
            'profits': []
        }
        
        for col in self.data.columns:
            col_str = str(col).lower()
            
            # 识别销售金额列
            if any(keyword in col_str for keyword in sales_keywords):
                identified_columns['sales_amount'].append(col)
            
            # 识别支付方式列
            if any(keyword in col_str for keyword in payment_keywords):
                identified_columns['payment_methods'].append(col)
            
            # 识别数量列
            if any(keyword in col_str for keyword in quantity_keywords):
                identified_columns['quantities'].append(col)
            
            # 识别利润列
            if any(keyword in col_str for keyword in profit_keywords):
                identified_columns['profits'].append(col)
        
        print("\n识别的列分类:")
        for category, columns in identified_columns.items():
            print(f"{category}: {columns}")
        
        return identified_columns
    
    def analyze_data_patterns(self):
        """
        分析数据模式和相关性
        """
        if self.data is None:
            print("❌ 请先加载数据")
            return None, None

        # 获取数值列
        numeric_columns = self.data.select_dtypes(include=[np.number]).columns.tolist()

        if len(numeric_columns) == 0:
            print("❌ 没有找到数值列")
            return None, None

        print(f"\n数值列: {numeric_columns}")

        # 计算相关性矩阵
        correlation_matrix = self.data[numeric_columns].corr()
        print("\n相关性矩阵:")
        print(correlation_matrix)

        # 分析每列的统计特征
        stats = {}
        for col in numeric_columns:
            col_data = self.data[col].dropna()
            if len(col_data) > 0:
                stats[col] = {
                    'mean': col_data.mean(),
                    'std': col_data.std(),
                    'min': col_data.min(),
                    'max': col_data.max(),
                    'median': col_data.median()
                }

        print("\n各列统计特征:")
        for col, stat in stats.items():
            print(f"{col}: 均值={stat['mean']:.2f}, 标准差={stat['std']:.2f}, 范围=[{stat['min']:.2f}, {stat['max']:.2f}]")

        return stats, correlation_matrix

    def build_prediction_models(self):
        """
        构建预测模型
        """
        if self.data is None:
            print("❌ 请先加载数据")
            return False

        print("\n正在构建预测模型...")

        # 准备训练数据
        train_data = self.data.dropna(subset=['实际销售额'])

        if len(train_data) < 5:
            print("❌ 训练数据不足")
            return False

        # 特征工程
        features = []
        target_columns = ['实际销售额', '微信', '（件）', '金包银金额', '足金、18K，紫金金额']

        # 基于实际销售额预测其他指标
        for target_col in target_columns[1:]:  # 除了实际销售额本身
            if target_col in train_data.columns:
                # 使用实际销售额作为特征
                X = train_data[['实际销售额']].dropna()
                y = train_data[target_col].dropna()

                # 确保X和y的索引对齐
                common_idx = X.index.intersection(y.index)
                if len(common_idx) < 3:
                    continue

                X_aligned = X.loc[common_idx]
                y_aligned = y.loc[common_idx]

                # 训练模型
                model = LinearRegression()
                model.fit(X_aligned, y_aligned)

                self.models[target_col] = model
                print(f"✓ {target_col} 预测模型训练完成")

        return True

    def predict_sales_indicators(self, target_sales_min=10000, target_sales_max=15000):
        """
        预测销售指标
        """
        if not self.models:
            print("❌ 请先构建预测模型")
            return None

        print(f"\n正在预测销售额在 {target_sales_min}-{target_sales_max} 元区间的各项指标...")

        # 使用目标销售额的中位数进行预测
        target_sales = (target_sales_min + target_sales_max) / 2

        predictions = {'实际销售额': target_sales}

        for indicator, model in self.models.items():
            try:
                pred_value = model.predict([[target_sales]])[0]
                predictions[indicator] = pred_value
                print(f"{indicator}: {pred_value:.2f}")
            except Exception as e:
                print(f"❌ 预测 {indicator} 失败: {str(e)}")

        return predictions

def main():
    """
    主函数
    """
    print("=" * 60)
    print("Excel销售数据分析工具")
    print("=" * 60)

    # 创建分析器实例
    analyzer = SalesDataAnalyzer("香阜7.xls")

    # 加载和分析数据结构
    if analyzer.load_and_analyze_structure():
        # 识别销售相关列
        identified_columns = analyzer.identify_sales_columns()

        # 分析数据模式
        stats, correlation = analyzer.analyze_data_patterns()

        # 构建预测模型
        if analyzer.build_prediction_models():
            # 进行预测
            predictions = analyzer.predict_sales_indicators(10000, 15000)

            if predictions:
                print("\n" + "=" * 60)
                print("预测结果汇总")
                print("=" * 60)
                for indicator, value in predictions.items():
                    print(f"{indicator}: {value:.2f}")

        print("\n" + "=" * 60)
        print("数据分析完成")
        print("=" * 60)
    else:
        print("❌ 数据分析失败")

if __name__ == "__main__":
    main()
